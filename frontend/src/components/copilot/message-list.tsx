'use client';

import { EnhancedMessage } from './enhanced-message';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

interface MessageListProps {
  messages: Message[];
}

export function MessageList({ messages }: MessageListProps) {
  const renderMessage = (message: Message) => {
    try {
      return (
        <EnhancedMessage
          key={message.id}
          content={message.content}
          data={message.data}
          timestamp={message.timestamp}
          type={message.type}
        />
      );
    } catch (error) {
      console.error('Error rendering message:', error);
      return (
        <div key={message.id} className="p-4 border border-red-200 rounded-lg bg-red-50">
          <p className="text-sm text-red-600">Error rendering message</p>
          <p className="text-xs text-red-500 mt-1">{message.content}</p>
        </div>
      );
    }
  };

  return (
    <div className="space-y-4">
      {messages.map(renderMessage)}
    </div>
  );
}
